#ifndef PLAYER_H
#define PLAYER_H

#include <QMainWindow>
#include <QTimer>
#include <QKeyEvent>
#include <QMouseEvent>
#include <gst/gst.h>
namespace Ui {
class player;
}

class player : public QMainWindow
{
    Q_OBJECT

public:
    explicit player(QWidget *parent = nullptr, const QString &videoPath = QString());
    ~player();

private slots:
    void onPlayPauseClicked();
    void onRewindClicked();
    void onFastForwardClicked();
    void onExitClicked();
    void onProgressSliderPressed();
    void onProgressSliderReleased();
    void onProgressSliderValueChanged(int value);
    void updatePosition();
    void checkGStreamerMessages();

private:
    Ui::player *ui;

    // GStreamer相关
    GstElement *pipeline;
    GstElement *playbin;
    GstBus *bus;

    // 定时器用于更新播放进度
    QTimer *positionTimer;

    // 控制条隐藏定时器
    QTimer *hideControlsTimer;

    // GStreamer消息检查定时器
    QTimer *messageTimer;

    // 播放状态
    bool isPlaying;
    bool isSliderPressed;
    gint64 duration;
    bool controlsVisible;

    // 私有方法
    void initializeGStreamer();
    void setupConnections();
    void loadVideo(const QString &filePath);
    void updateTimeLabel(gint64 current, gint64 total);
    QString formatTime(gint64 time);
    void seek(gint64 position);
    void showControls();
    void hideControls();
    void resetHideTimer();

protected:
    void keyPressEvent(QKeyEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    bool eventFilter(QObject *obj, QEvent *event) override;
};

#endif // PLAYER_H
